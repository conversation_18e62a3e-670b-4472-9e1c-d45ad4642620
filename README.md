# Hướng dẫn

> <PERSON><PERSON><PERSON> ý: Nếu bạn dùng Linux nó sẽ không là vấn đề, nếu là windows thì tốc độ Docker khá là chậm nên hiệu suất làm việc của bạn sẽ có thể giảm tới 30%. C<PERSON> một số phương pháp tăng tốc tuy nhiên nhìn chung nó cũng khá là phức tạp cho người mới. Còn nếu bạn đã là pro-code thì chắc cũng không cần nói thêm nữa.

## Yêu cầu hệ thống

- Dĩ nhiên là cần cài xong docker phiên bản mới nhất. Xem [hướng dẫn tại đây](https://wiki.vinades.vn/tai-lieu:ky-thuat:docker:huong-dan-docker-nukeviet)
- Nế<PERSON> máy chạy windows, cần d<PERSON> tối thiểu 10gb RAM. Ứng dụng này chạy mất từ 8 - 10gb RAM.
- Trên windows cài đặt Git SCM https://git-scm.com/ và đảm bảo chạy được git bash. Trên linux cần cài git, curl
- Chỉnh sửa file hosts các domain sau

```
127.0.0.1 dauthau.local
127.0.0.1 daugianet.local
127.0.0.1 id.dauthau.local
127.0.0.1 sso.dauthau.local
127.0.0.1 db.dauthau.local
127.0.0.1 api.dauthau.local
127.0.0.1 crmprivate.dauthau.local
127.0.0.1 x1.dauthau.local
127.0.0.1 dauthaunet.local
127.0.0.1 file1.dauthau.local
```

- Đã được cấp quyền repoter trở lên ở các kho code sau:

```
https://vinades.org/dauthau/dauthau.info
https://vinades.org/dauthau/dauthau.net
https://vinades.org/dauthau/fileserver
https://vinades.org/dauthau/id.dauthau.net
https://vinades.org/dauthau/sso.dauthau.net
https://vinades.org/dauthau/api.dauthau.info
https://vinades.org/dauthau/crmprivate.dauthau.net
https://vinades.org/dauthau/dauthau.info-crontab-db
https://vinades.org/dauthau/dauthau-crawls
https://vinades.org/dauthau/sendmail-aws
https://vinades.org/dauthau/dauthau.net.private
https://vinades.org/dauthau/dauthau.net.crawls
```

- Đảm bảo đã cấu hình SSH hoặc Personal Access Tokens để clone thành công các kho code trên vinades.org
- Đã cài đặt NodeJS, Sass, Less, sass2less theo [hướng dẫn tại đây](https://vinades.org/dauthau/dauthau.net/-/blob/master/dev/README.md#h%C6%B0%E1%BB%9Bng-d%E1%BA%ABn-c%C3%A0i-%C4%91%E1%BA%B7t). Cài được package là xong, không cần build theo hướng dẫn đó.
- Đảm bảo máy chưa có ứng dụng nào chạy port 9200 và 9201

## Hướng dẫn cài đặt

- Clone kho code này về một thư mục. Đảm bảo dung lượng thư mục còn trống ít nhất 3gb.
- Vào thư mục đó mở git bash lên gõ `bash setup.sh` và làm theo các yêu cầu cho đến khi màn hình hiển thị là `Xong!`
- Nếu đã setup trước đó rồi bạn cũng có thể chạy setup.sh lại lần nữa để build các container mà không phải lấy lại code và dữ liệu. Bạn cũng có thể xóa hết code, dữ liệu khi chạy lại setup.sh tùy cách bạn trả lời lúc chạy.
- Để nhận SSL tự sinh lúc truy cập các site thì import file conf/nginx/server.crt theo [hướng dẫn này](https://vinades.org/dauthau/hstdt-docker/-/blob/main/ssl/README.md#import-servercrt-v%C3%A0o-trusted-root-certification-authorities-tr%C3%AAn-windows) 

## Truy cập các site và lập trình

Thư mục src chứa toàn bộ các file code của các kho  
 
**Danh sách các domain**

- **https://dauthau.local:8443** tương ứng dauthau.asia
- **https://daugianet.local:8443** tương ứng daugia.net
- **https://id.dauthau.local:8443** tương ứng id.dauthau.net
- **https://sso.dauthau.local:8443** tương ứng sso.dauthau.net
- **https://db.dauthau.local:8443** địa chỉ truy cập phpmyadmin của toàn bộ các site. Tài khoản root và mật khẩu để trống
- **https://api.dauthau.local:8443** tương ứng api.dauthau.asia
- **https://crmprivate.dauthau.local:8443** tương ứng crmprivate.dauthau.net
- **https://x1.dauthau.local:8443** tương ứng x1.dauthau.asia
- **https://dauthaunet.local:8443** tương ứng dauthau.net
- **https://file1.dauthau.local:8443** tương ứng file1.dauthau.net

Nếu muốn truy cập phiên bản không SSL thì thay https bằng http và port 8443 thành 8080. Ví dụ **http://dauthau.local:8080**

**Elasticsearch**

- **http://localhost:9200** máy chủ ES DauThau: Bidding
- **http://localhost:9201** máy chủ ES DauThau-Net: CRM, Bidding Mail, DauGia, Result
