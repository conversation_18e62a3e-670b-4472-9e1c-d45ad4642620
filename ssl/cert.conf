[ req ]

default_bits        = 2048
default_keyfile     = server-key.pem
distinguished_name  = subject
req_extensions      = req_ext
x509_extensions     = x509_ext
string_mask         = utf8only

[ subject ]

countryName                 = Country Name (2 letter code)
countryName_default         = VN

stateOrProvinceName         = State or Province Name (full name)
stateOrProvinceName_default = Ha Noi

localityName                = Locality Name (eg, city)
localityName_default        = Ha Noi

organizationName            = Organization Name (eg, company)
organizationName_default    = VINADES.,JSC

commonName                  = Common Name (e.g. server FQDN or YOUR name)
commonName_default          = dauthau.local

emailAddress                = Email Address
emailAddress_default        = <EMAIL>

[ x509_ext ]

subjectKeyIdentifier   = hash
authorityKeyIdentifier = keyid,issuer

basicConstraints       = CA:FALSE
keyUsage               = digitalSignature, keyEncipherment
subjectAltName         = @alternate_names
nsComment              = "OpenSSL Generated Certificate"

[ req_ext ]

subjectKeyIdentifier = hash

basicConstraints     = CA:FALSE
keyUsage             = digitalSignature, keyEncipherment
subjectAltName       = @alternate_names
nsComment            = "OpenSSL Generated Certificate"

[ alternate_names ]

DNS.1       = dauthau.local
DNS.2       = dauthaunet.local
DNS.3       = *.dauthau.local
DNS.4       = *.dauthaunet.local
DNS.5       = daugianet.local