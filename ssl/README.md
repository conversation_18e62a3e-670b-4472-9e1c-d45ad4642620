# Sinh SSL và import

> Hướng dẫn này dùng cho windows

Yêu cầu cài openssl. Nếu bạn đã cài Git-SCM thì nó có sẵn openssl, bạn có thể gõ `openssl` trong git bash nếu nó chạy là ok. Nếu chưa có openssl thì cài ở đây https://slproweb.com/products/Win32OpenSSL.html

Trường hợp openssl lỗi nên cài lại phiên bản Git-SCM mới nhất là ok.

Sau đó chạy `bash generate-ssl.sh` rồi gõ thông số hoặc cứ ấn enter đến xong rồi copy 2 file server.crt và server.key vào conf/nginx

## Import server.crt vào Trusted Root Certification Authorities trên windows

- Click đúp server.crt chọn Install Certificate 
- Chọn Local Machine trong Store Location
- Tiếp tục chọn “Place all certificate in the following store” và click browse sau đó chọn Trusted Root Certification Authorities.
- Chọn Next và Finish.
