networks:
  hstdt:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16
          ip_range: **********/24

services:
  dauthau_info:
    container_name: dauthau_info
    hostname: dauthau_info
    build:
      dockerfile: Dockerfile
      context: ./conf/php84
    volumes:
      - ./src/dauthau.info/src:/var/www/html
    networks:
      hstdt:
        ipv4_address: **********
    environment:
      TZ: ${TimeZone}
    depends_on:
      - dauthau_db

  dauthau_id:
    container_name: dauthau_id
    hostname: dauthau_id
    build:
      dockerfile: Dockerfile
      context: ./conf/php84
    volumes:
      - ./src/id.dauthau.net/src:/var/www/html
    networks:
      hstdt:
        ipv4_address: **********
    environment:
      TZ: ${TimeZone}
    depends_on:
      - dauthau_db

  dauthau_sso:
    container_name: dauthau_sso
    hostname: dauthau_sso
    build:
      dockerfile: Dockerfile
      context: ./conf/php82
    volumes:
      - ./src/sso.dauthau.net:/var/www/html
    networks:
      hstdt:
        ipv4_address: **********
    environment:
      TZ: ${TimeZone}
    depends_on:
      - dauthau_db

  dauthau_db:
    container_name: dauthau_db
    hostname: dauthau_db
    image: mariadb:11.4
    volumes:
      - ./conf/my.cnf:/etc/mysql/conf.d/dauthau.cnf
      - ./_docker/mysql:/var/lib/mysql
      - ./db:/home
    environment:
      MARIADB_ALLOW_EMPTY_ROOT_PASSWORD: 1
      TZ: ${TimeZone}
    command: >
      bash -c " chmod 644 /etc/mysql/conf.d/dauthau.cnf && /usr/local/bin/docker-entrypoint.sh mariadbd "
    networks:
      hstdt:
        ipv4_address: **********

  dauthau_phpmyadmin:
    container_name: dauthau_phpmyadmin
    hostname: dauthau_phpmyadmin
    image: phpmyadmin:apache
    environment:
      PMA_HOST: dauthau_db
      PMA_USER: root
      TZ: ${TimeZone}
    volumes:
      - ./uploads.ini:/usr/local/etc/php/conf.d/uploads.ini:ro
    depends_on:
      - dauthau_db
    networks:
      hstdt:
        ipv4_address: **********

  dauthau_reverse_proxy:
    container_name: dauthau_reverse_proxy
    image: nginx:latest
    volumes:
      - ./conf/nginx:/etc/nginx/conf.d
    environment:
      TZ: ${TimeZone}
    ports:
      - 8080:80
      - 8443:443
    networks:
      hstdt:
        ipv4_address: **********
    depends_on:
      - dauthau_info
      - dauthau_id

  dauthau_api:
    container_name: dauthau_api
    hostname: dauthau_api
    build:
      dockerfile: Dockerfile_without_cors
      context: ./conf/php84
    volumes:
      - ./src/api.dauthau.info/src:/var/www/html
    networks:
      hstdt:
        ipv4_address: **********
    environment:
      TZ: ${TimeZone}
    depends_on:
      - dauthau_db

  dauthau_crmprivate:
    container_name: dauthau_crmprivate
    hostname: dauthau_crmprivate
    build:
      dockerfile: Dockerfile
      context: ./conf/php84
    volumes:
      - ./src/crmprivate.dauthau.net/public_html:/var/www/html
      - ./src/crmprivate.dauthau.net/private:/var/www/private
    networks:
      hstdt:
        ipv4_address: **********
    environment:
      TZ: ${TimeZone}
    depends_on:
      - dauthau_db

  dauthau_crontabdb:
    container_name: dauthau_crontabdb
    hostname: dauthau_crontabdb
    build:
      dockerfile: Dockerfile
      context: ./conf/php84
    volumes:
      - ./src/dauthau.info-crontab-db/public_html:/var/www/html
      - ./src/dauthau.info-crontab-db/private:/var/www/private
    networks:
      hstdt:
        ipv4_address: **********
    environment:
      TZ: ${TimeZone}
    depends_on:
      - dauthau_db

  dauthau_crawls:
    container_name: dauthau_crawls
    hostname: dauthau_crawls
    build:
      dockerfile: Dockerfile
      context: ./conf/php84
    volumes:
      - ./src/dauthau-crawls:/var/www/private
    networks:
      hstdt:
        ipv4_address: ***********
    environment:
      TZ: ${TimeZone}
    depends_on:
      - dauthau_db

  dauthau_sendmailaws:
    container_name: dauthau_sendmailaws
    hostname: dauthau_sendmailaws
    build:
      dockerfile: Dockerfile
      context: ./conf/php84
    volumes:
      - ./src/sendmail-aws:/var/www/private
    networks:
      hstdt:
        ipv4_address: **********1
    environment:
      TZ: ${TimeZone}
    depends_on:
      - dauthau_db

  es_dauthau:
    container_name: es_dauthau
    hostname: elastic_dbhost
    image: elasticsearch:7.17.28
    environment:
      - xpack.security.enabled=false
      - discovery.type=single-node
    ports:
      - 9200:9200
    mem_limit: 1g
    volumes:
      - ./_docker/es_dt:/usr/share/elasticsearch/data
    networks:
      hstdt:
        ipv4_address: **********2

  es_dauthaunet:
    container_name: es_dauthaunet
    hostname: elastic_dtnet
    image: elasticsearch:7.17.28
    environment:
      - xpack.security.enabled=false
      - discovery.type=single-node
    ports:
      - 9201:9200
    mem_limit: 1g
    volumes:
      - ./_docker/es_dtnet:/usr/share/elasticsearch/data
    networks:
      hstdt:
        ipv4_address: **********3

  dauthau_net:
    container_name: dauthau_net
    hostname: dauthau_net
    build:
      dockerfile: Dockerfile
      context: ./conf/php84
    volumes:
      - ./src/dauthau.net/src:/var/www/html
    networks:
      hstdt:
        ipv4_address: **********4
    environment:
      TZ: ${TimeZone}
    depends_on:
      - dauthau_db

  dauthau_netprivate:
    container_name: dauthau_netprivate
    hostname: dauthau_netprivate
    build:
      dockerfile: Dockerfile
      context: ./conf/php84
    volumes:
      - ./src/dauthau.net.private:/var/www/private
    networks:
      hstdt:
        ipv4_address: **********5
    environment:
      TZ: ${TimeZone}
    depends_on:
      - dauthau_db

  dauthau_netcrawls:
    container_name: dauthau_netcrawls
    hostname: dauthau_netcrawls
    build:
      dockerfile: Dockerfile
      context: ./conf/php84
    volumes:
      - ./src/dauthau.net.crawls:/var/www/private
    networks:
      hstdt:
        ipv4_address: ***********
    environment:
      TZ: ${TimeZone}
    depends_on:
      - dauthau_db

  dauthau_zookeeper:
    container_name: dauthau_zookeeper
    hostname: dauthau_zookeeper
    image: 'bitnamilegacy/zookeeper:latest'
    environment:
      - ALLOW_ANONYMOUS_LOGIN=yes
    volumes:
      - ./_docker/zookeeper:/bitnami
    networks:
      hstdt:
        ipv4_address: ***********

  dauthau_kafka:
    image: 'wurstmeister/kafka:latest'
    container_name: dauthau_kafka
    hostname: dauthau_kafka
    environment:
      - KAFKA_ZOOKEEPER_CONNECT=dauthau_zookeeper:2181
      - KAFKA_ADVERTISED_LISTENERS=PLAINTEXT://dauthau_kafka:9092
      - KAFKA_LISTENERS=PLAINTEXT://0.0.0.0:9092
      - KAFKA_AUTO_CREATE_TOPICS_ENABLE=true
    depends_on:
      - dauthau_zookeeper
    networks:
      hstdt:
        ipv4_address: ***********
    volumes:
      - ./_docker/kafka:/kafka
    mem_limit: 512m

  dauthau_fileserver:
    container_name: dauthau_fileserver
    hostname: dauthau_fileserver
    build:
      dockerfile: Dockerfile
      context: ./conf/php84
    volumes:
      - ./src/fileserver:/var/www/html
    networks:
      hstdt:
        ipv4_address: ***********
    environment:
      TZ: ${TimeZone}
    depends_on:
      - dauthau_db

  dauthau_idapi:
    container_name: dauthau_idapi
    hostname: dauthau_idapi
    build:
      dockerfile: Dockerfile
      context: ./conf/golang
    volumes:
      - ./conf/nginx/server.crt:/root/ssl.crt
      - ./conf/nginx/server.key:/root/ssl.key
    networks:
      hstdt:
        ipv4_address: ***********
    environment:
      TZ: ${TimeZone}
    depends_on:
      - dauthau_db
