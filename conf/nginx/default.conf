server {
    listen       80;
    server_name  localhost;

    location / {
        root   /usr/share/nginx/html;
        index  index.html index.htm;
    }

    error_page   500 502 503 504  /50x.html;
    location = /50x.html {
        root   /usr/share/nginx/html;
    }
}

server {
    listen 80;
    server_name dauthau.local daugianet.local;

    location / {
        proxy_pass http://dauthau_info:8080;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_set_header X-Forwarded-Port 8080;
    }
}

server {
    listen 80;
    server_name dauthaunet.local;

    location / {
        proxy_pass http://dauthau_net:8080;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_set_header X-Forwarded-Port 8080;
		
		proxy_buffer_size          32k;
		proxy_buffers              8 64k;
		proxy_busy_buffers_size    128k;
		proxy_max_temp_file_size   0;
    }
}

server {
    listen 80;
    server_name id.dauthau.local;

    location / {
        proxy_pass http://dauthau_id:8080;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_set_header X-Forwarded-Port 8080;
    }
}

server {
    listen 80;
    server_name db.dauthau.local;

    location / {
        client_max_body_size 100M;
        proxy_pass http://dauthau_phpmyadmin;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
}

server {
    listen 80;
    server_name sso.dauthau.local;

    location / {
        proxy_pass http://dauthau_sso:8080;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_set_header X-Forwarded-Port 8080;
    }
}

server {
    listen 80;
    server_name api.dauthau.local;

    location / {
        proxy_pass http://dauthau_api:8080;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_set_header X-Forwarded-Port 8080;
    }
}

server {
    listen 80;
    server_name crmprivate.dauthau.local;

    location / {
        proxy_pass http://dauthau_crmprivate:8080;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_set_header X-Forwarded-Port 8080;
    }
}

server {
    listen 80;
    server_name x1.dauthau.local;

    location / {
        proxy_pass http://dauthau_crontabdb:8080;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_set_header X-Forwarded-Port 8080;
    }
}

server {
    listen 80;
    server_name file1.dauthau.local;

    location / {
        proxy_pass http://dauthau_fileserver:8080;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_set_header X-Forwarded-Port 8080;
    }
}
