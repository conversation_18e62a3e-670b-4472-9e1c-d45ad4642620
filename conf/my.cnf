# Config for HSTDT
[mariadb]
max_allowed_packet=512M
wait_timeout=28800
net_read_timeout=600
net_write_timeout=600
innodb_log_file_size=512M

#sql_mode=STRICT_TRANS_TABLES,ERROR_FOR_DIVISION_BY_ZERO,NO_AUTO_CREATE_USER,NO_ENGINE_SUBSTITUTION
#skip-name-resolve=1
#tmp_table_size=64M
#max_heap_table_size=64M
#key_buffer_size=64M
#innodb_buffer_pool_size=3G
#performance_schema=OFF
#table_definition_cache=-1
#innodb_log_file_size=512M
#max_allowed_packet=256M
#read_buffer_size = 8MB
#read_rnd_buffer_size = 8MB
#innodb_temp_data_file_path=ibtmp1:12M:autoextend:max:500M
