FROM alpine:latest
ENV TIMEZONE="Asia/Ho_Chi_Minh"

# Install glibc compatibility layer
RUN apk --no-cache add ca-certificates wget && \
    wget -q -O /etc/apk/keys/sgerrand.rsa.pub https://alpine-pkgs.sgerrand.com/sgerrand.rsa.pub && \
    wget https://github.com/sgerrand/alpine-pkg-glibc/releases/download/2.34-r0/glibc-2.34-r0.apk && \
    apk add --force-overwrite glibc-2.34-r0.apk && \
    rm glibc-2.34-r0.apk

# Install timezone data and keep it
RUN apk update && apk upgrade && apk add --no-cache tzdata && \
    cp /usr/share/zoneinfo/${TIMEZONE} /etc/localtime && \
    echo "${TIMEZONE}" > /etc/timezone

COPY --chmod=777 --chown=root idapi.release /root/idapi.release
CMD ["/root/idapi.release"]
WORKDIR /root
